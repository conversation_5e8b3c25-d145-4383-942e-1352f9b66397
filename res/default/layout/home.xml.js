/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
const e=function(e={}){var{parentView:t,context:i,model:r,rootView:o,themeInstance:n}=e;const a=require("/usr/framework/yunos/ui/view/CompositeView.js");const u=require("/usr/framework/yunos/ui/markup/Util.js");const d=require("/usr/framework/yunos/ui/markup/ResourceTable.js");let s=d.getInstance(e);const l=require("/usr/framework/yunos/ui/view/WebView.js");var c=require("/usr/framework/yunos/ui/view/View.js");const w=require("/usr/framework/yunos/device/Screen.js");let _=w.getInstance();const y=require("/usr/framework/yunos/ui/view/ImageView.js");const p=require("/usr/framework/yunos/ui/view/TextView.js");const g=require("/usr/framework/yunos/ui/theme/Theme.js");let h=g.getInstance(i);const m=require("/usr/framework/extend/hdt/control/ButtonBM.js");var v;var b;if(e.externalConverter&&"function"==typeof e.externalConverter&&(v=e.externalConverter),e.returnStructureNode)return u.formatNodeinfo({name:"CompositeView",attributes:{id:"id_rootview",layout:"{layout.container}"},_children:[{name:"WebView",attributes:{id:"id_webview"},_children:[]},{name:"CompositeView",attributes:{id:"id_network_container",layout:"{layout.network_container}",background:"{color.PAGE_BG_COLOR}",visibility:"{enum.View.Visibility.None}"},_children:[{name:"CompositeView",attributes:{id:"id_network_error",width:"{sdp(300)}",height:"{sdp(255)}",layout:"{layout.network_error}"},_children:[{name:"ImageView",attributes:{id:"id_error_icon",width:"{sdp(140)}",height:"{sdp(140)}",src:"{img(images/ic_no_wifi.png)}",scaleType:"{enum.ImageView.ScaleType.Center}"},_children:[]},{name:"TextView",attributes:{id:"id_error_text",text:"{string.NETWORK_ERROR}",propertySetName:"extend/hdt/FontBody2",color:"{theme.color.White_1}"},_children:[]},{name:"ButtonBM",attributes:{id:"id_error_btn",text:"{string.RELOAD}",height:"{sdp(50)}"},_children:[]}]}]}]},e);var o;(o=o||new a(n,i)).id="id_rootview",o.layout=u.getLayoutValue("layout.container",s,e);var x=new l(n,i);x.id="id_webview",x.addToMarkupParent(o);var x=new a(n,i);x.id="id_network_container",x.layout=u.getLayoutValue("layout.network_container",s,e),x.background=s.getValue("PAGE_BG_COLOR","color.json"),x.visibility=c.Visibility.None;var c=new a(n,i);c.id="id_network_error",c.width=_.getPixelBySDp(300),c.height=_.getPixelBySDp(255),c.layout=u.getLayoutValue("layout.network_error",s,e);var e=new y(n,i);e.id="id_error_icon",e.width=_.getPixelBySDp(140),e.height=_.getPixelBySDp(140),e.src="page://bilibili.alios.cn/asset/images/ic_no_wifi.png",e.scaleType=y.ScaleType.Center,e.addToMarkupParent(c);var e=new p(n,i);e.id="id_error_text",e.text=s.getString("NETWORK_ERROR"),e.color=h.getColor("White_1"),e.addToMarkupParent(c);var n=new m(n,i);return n.id="id_error_btn",n.text=s.getString("RELOAD"),n.height=_.getPixelBySDp(50),n.addToMarkupParent(c),c.addToMarkupParent(x),x.addToMarkupParent(o),e&&(e.propertySetName="extend/hdt/FontBody2"),t&&(o.addToMarkupParent(t),o=t),o};exports.creator=e;