/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"bridge/ContainerBridge.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/core/EventEmitter.js","name":""},{"path":"/usr/framework/yunos/ui/view/View.js","name":""},{"path":"/usr/framework/yunos/ui/voice/VoiceCommand.js","name":""},{"path":"/usr/framework/yunos/ui/view/WebView.js","name":""},{"path":"/usr/framework/yunos/web/WebViewClient.js","name":""},{"path":"/usr/framework/extend/hdt/control/ButtonBM.js","name":""},{"path":"/usr/framework/extend/hdt/control/LoadingPageBM.js","name":""},{"path":"/usr/framework/yunos/page/Page.js","name":""},{"path":"/usr/framework/yunos/page/PageLink.js","name":""},{"path":"/usr/framework/yunos/ui/view/TextView.js","name":""},{"path":"/usr/framework/yunos/ui/event/TouchEvent.js","name":""},{"path":"../service/AccountService.js","name":""},{"path":"../service/AudioService.js","name":""},{"path":"../service/BrightnessService.js","name":""},{"path":"../service/DeviceService.js","name":""},{"path":"../service/HardKeyService.js","name":""},{"path":"../service/NetworkService.js","name":""},{"path":"../service/CarService.js","name":""},{"path":"../Consts.js","name":""},{"path":"../utils/log.js","name":""},{"path":"../utils/Utils.js","name":""},{"path":"../utils/TrafficHelper.js","name":""},{"path":"../utils/UserTrackHelper.js","name":""},{"path":"../index.js","name":""},{"path":"../components/VideoLimitView.js","name":""},{"path":"/usr/framework/yunos/ui/view/CompositeView.js","name":""},{"instance_of":30},{"oname":"","fields":{"PLAY":-3,"PAUSE":-3}},{"oname":"","fields":{"NONE":-5,"PLAY":-5,"PAUSE":-5}},{"cname":"ContainerBridge","supers":{"vb":7},"fields":{"isDestroyed":2,"webview":38,"videoInfo":0,"delayShowCloseImageTimer":0,"isPageActivated":2,"isPlayPage":2,"lastState":3,"voiceCommend":1,"checkFlowTimer":0,"rootview":39,"onSpeedChanged":0,"onGearStatusChanged":0,"onHardKeyStateChanged":0,"onAudioMuteChanged":0,"onAudioSessionChanged":0,"onConnectStateChanged":0,"onTrafficChanged":0,"onDrivingWarning":0,"warningPageShowing":2,"warningPage":53,"hideWarningTimer":0},"sfields":{"static instance":36,"const getInstance":40,"const releaseInstance":41},"methods":[37,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67]},{"fname":"constructor","ret":36},{"instance_of":10},{"instance_of":32},{"fname":"static getInstance","ret":36},{"fname":"static releaseInstance"},{"fname":"init","params":{"tc":39}},{"fname":"setWebView","params":{"tc":38}},{"fname":"initListener","private":1},{"fname":"injectJsObject","private":1},{"fname":"callJsObject","private":1},{"fname":"loadBridge","private":1,"params":{"tc":38}},{"fname":"handleJSMessage","private":1,"params":{"tc":3}},{"fname":"setScreenMode","private":1,"params":{"tc":1}},{"fname":"setMuteStatus","private":1,"params":{"tc":1}},{"fname":"sendOperateEvent","private":1,"params":{"tc":0}},{"fname":"playCondition","private":1,"ret":2},{"instance_of":31},{"fname":"showWarningPage","private":1},{"fname":"hideWarningPage","private":1,"params":{"tc":2}},{"fname":"onBackPressed","private":1},{"fname":"removeInterval","private":1,"params":{"tc":0}},{"fname":"playVideo","private":1},{"fname":"pauseVideo","private":1},{"fname":"setLastState","private":1},{"fname":"setCheckFlowTimer","private":1},{"fname":"removeCheckFlowTimer","private":1},{"fname":"notifyAppEvent","params":{"tc":3}},{"fname":"addVoiceCommands","private":1},{"fname":"removeVoiceCommands","private":1},{"fname":"removeTimeout","private":1,"params":{"tc":0}},{"fname":"destroy","hobj":1},{"fname":"","params":{"tc":1}},{"fname":"","params":{"tc":1}},{"fname":"","params":{"tc":2,"uc":2}},{"fname":"","params":{"tc":3,"uc":1}},{"fname":"","params":{"tc":2}},{"fname":"","params":{"tc":2,"uc":3}},{"fname":"","params":{"tc":1}},{"fname":"","params":{"tc":2}},{"fname":"","ret":3},{"fname":"","ret":1},{"fname":"","ret":1},{"fname":"","ret":1},{"fname":"","params":{"vc":1}},{"fname":"","ret":1},{"fname":"","params":{"vc":1}},{"fname":"","ret":1},{"fname":"","ret":2},{"fname":"","ret":5},{"fname":"","ret":2},{"oname":"","fields":{"getVehicleInfo":-76,"getVehicleGearStatus":-77,"getVehicleSpeed":-78,"getAudioVolumeLevel":-79,"setAudioVolumeLevel":-80,"getScreenBrightness":-81,"setScreenBrightness":-82,"getNetworkStatus":-83,"getNetworkValid":-84,"requestAudioSession":-85,"getAmbientLightStatus":-86}},{"oname":"","fields":{"product":-3,"resolution":-3,"orientation":-3,"uuid":-3,"accountId":-3}},{"aname":"","element":3,"mode":"normal"},{"fname":"","params":{"uc":38,"vc":3}},{"fname":"","params":{"uc":1,"vc":3,"wc":1,"xc":3}},{"aname":"","element":0,"mode":"normal"},{"oname":"","fields":{"width":-1}},{"oname":"","fields":{"height":-1}},{"fname":""},{"oname":"","fields":{"video_title":-3,"video_url":-3,"operation":-3}},{"fname":""},{"oname":"","fields":{"locate":-3}},{"fname":"","params":{"vc":100,"wc":100}},{"bname":"Object"},{"fname":""},{"oname":"","fields":{"type":-3}},{"fname":""},{"fname":"","params":{"vc":3,"wc":1}}],"exports":37}*/"use strict";const /*<@7>*/vb = require("/usr/framework/yunos/core/EventEmitter.js");const /*<@8>*/wb = require("/usr/framework/yunos/ui/view/View.js");const /*<@9>*/xb = require("/usr/framework/yunos/ui/voice/VoiceCommand.js");const yb = xb.RecognitionMode;const /*<@11>*/Ab = require("/usr/framework/yunos/web/WebViewClient.js");const /*<@14>*/Db = require("/usr/framework/yunos/page/Page.js");const /*<@15>*/Eb = require("/usr/framework/yunos/page/PageLink.js");const /*<@18>*/Hb = require("/opt/app/bilibili.alios.cn/src/service/AccountService.js");const /*<@19>*/Ib = require("/opt/app/bilibili.alios.cn/src/service/AudioService.js");const /*<@20>*/Jb = require("/opt/app/bilibili.alios.cn/src/service/BrightnessService.js");const /*<@21>*/Kb = require("/opt/app/bilibili.alios.cn/src/service/DeviceService.js");const /*<@22>*/Lb = require("/opt/app/bilibili.alios.cn/src/service/HardKeyService.js");const /*<@23>*/Mb = require("/opt/app/bilibili.alios.cn/src/service/NetworkService.js");const /*<@24>*/Nb = require("/opt/app/bilibili.alios.cn/src/service/CarService.js");const /*<@25>*/Ob = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@26>*/Pb = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@27>*/Qb = require("/opt/app/bilibili.alios.cn/src/utils/Utils.js");const /*<@28>*/Rb = require("/opt/app/bilibili.alios.cn/src/utils/TrafficHelper.js");const /*<@29>*/Sb = require("/opt/app/bilibili.alios.cn/src/utils/UserTrackHelper.js");const /*<@30>*/Tb = require("/opt/app/bilibili.alios.cn/src/index.js");const /*<@31>*/Ub = require("/opt/app/bilibili.alios.cn/src/components/VideoLimitView.js");const Wb = Tb.getInstance();const /*<@3>*/Xb = "ContainerBridge";const /*<@5>*/Yb = 5000;const /*<@5>*/Zb = 1500;const /*<@34>*/ac = /*<@34>*/{PLAY:"play",PAUSE:"pause"};const /*<@35>*/bc = /*<@35>*/{NONE:0,PLAY:1,PAUSE:2};let /*<@3>*/cc = "javascript:(function() {"+"var eleVideo = document.getElementsByTagName('video')[0];"+"if (eleVideo) {"+"    eleVideo.addEventListener(\"play\",function(){"+"        var title;"+"        var eleTitle = document.querySelector('.bili-player-bar__title');"+"        if (eleTitle) {"+"            title = eleTitle.innerHTML;"+"        } else {"+"            eleTitle = document.getElementsByTagName('title')[0];"+"            if (eleTitle) {"+"                title = eleTitle.text;"+"            }"+"        }"+"        console.log('sendOperateEvent:', JSON.stringify({title:title, src:eleVideo.src, operate:\"play\"}));"+"    });"+"    eleVideo.addEventListener(\"pause\",function(){"+"        var title;"+"        var eleTitle = document.querySelector('.bili-player-bar__title');"+"        if (eleTitle) {"+"            title = eleTitle.innerHTML;"+"        } else {"+"            eleTitle = document.getElementsByTagName('title')[0];"+"            if (eleTitle) {"+"                title = eleTitle.text;"+"            }"+"        }"+"        console.log('sendOperateEvent:', JSON.stringify({title:title, src:eleVideo.src, operate:\"pause\"}));"+"    });"+"    eleVideo.addEventListener(\"end\",function(){"+"        var title;"+"        var eleTitle = document.querySelector('.bili-player-bar__title');"+"        if (eleTitle) {"+"            title = eleTitle.innerHTML;"+"        } else {"+"            eleTitle = document.getElementsByTagName('title')[0];"+"            if (eleTitle) {"+"                title = eleTitle.text;"+"            }"+"        }"+"        console.log('sendOperateEvent:', JSON.stringify({title:title, src:eleVideo.src, operate:\"end\"}));"+"    });"+"}"+"})()";class /*<@37>*/ContainerBridge extends vb{constructor(){super(...arguments);this.warningPageShowing=false;}static /*<@40>*/getInstance(){if(!this.instance) {this.instance=new ContainerBridge();}return this.instance;}static /*<@41>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@42>*/init(/*<@39>*/tc){this.rootview=tc.findViewById("id_rootview");}/*<@43>*/setWebView(/*<@38>*/tc){this.isDestroyed=false;this.webview=tc;this.isPageActivated=true;this.isPlayPage=false;this.lastState=ac.PLAY;this.voiceCommend=bc.NONE;this.videoInfo={};this.initListener();this.injectJsObject();this.callJsObject();}/*<@44>*/initListener(){Hb.getInstance().init();Nb.getInstance().init();Mb.getInstance().init();Jb.getInstance().init();Lb.getInstance().init(this.webview);this.onSpeedChanged=/*<@68>*/(/*<@1>*/tc) =>{if(this.isDestroyed || !this.webview || !this.isPageActivated) {return;}Pb.I(Xb, "onSpeedChanged, speed:", tc);this.webview.evaluateJavaScript(`injectJsObject.notifyVehicleSpeed(${JSON.stringify(tc)})`);};Nb.getInstance().on(Ob.EV_SPEED_CHANGED, this.onSpeedChanged);this.onGearStatusChanged=/*<@69>*/(/*<@1>*/tc) =>{if(this.isDestroyed || !this.webview || !this.isPageActivated) {return;}Pb.I(Xb, "onGearStatusChanged, status:", tc);this.webview.evaluateJavaScript(`injectJsObject.notifyVehicleGearStatus(${JSON.stringify(tc)})`);};Nb.getInstance().on(Ob.EV_GEAR_STATUS_CHANGED, this.onGearStatusChanged);this.onDrivingWarning=/*<@70>*/(/*<@2>*/tc, /*<@2>*/uc = false) =>{if(this.isDestroyed || !this.webview || !this.isPageActivated) {return;}Pb.I(Xb, "videoLimitTag onDrivingWarning,", tc, this.warningPageShowing);if(tc) {this.showWarningPage();}else {if(uc) {this.hideWarningPage(false);this.playVideo();}}};Nb.getInstance().on(Ob.EV_DRIVING_WARNING, this.onDrivingWarning);this.onHardKeyStateChanged=/*<@71>*/(/*<@3>*/tc, /*<@1>*/uc) =>{if(this.isDestroyed || !this.webview || !this.isPageActivated) {return;}Pb.I(Xb, "onHardKeyStateChanged, methodName:", tc, ", value:", uc);Ib.getInstance().requestAudioSession();switch (tc) {case Ob.HardKeyOperate.NEXT:this.webview.evaluateJavaScript(`injectJsObject.notifyKeyEvent('next')`);break;case Ob.HardKeyOperate.PREV:this.webview.evaluateJavaScript(`injectJsObject.notifyKeyEvent('prev')`);break;case Ob.HardKeyOperate.PLAY_PAUSE:this.webview.evaluateJavaScript(`injectJsObject.notifyKeyEvent('start')`);break;case Ob.HardKeyOperate.SEEK:break;default:break;}};Lb.getInstance().on(Ob.EV_HARDKEY_STATE_CHANGED, this.onHardKeyStateChanged);this.onAudioMuteChanged=/*<@72>*/(/*<@2>*/tc) =>{if(this.isDestroyed || !this.webview || !this.isPageActivated) {return;}Pb.I(Xb, "onAudioMuteChanged, mute:", tc);if(tc) {this.webview.evaluateJavaScript(`injectJsObject.notifyKeyEvent(${JSON.stringify("mute")})`);this.lastState=ac.PAUSE;}else {this.webview.evaluateJavaScript(`injectJsObject.notifyKeyEvent(${JSON.stringify("unmute")})`);this.lastState=ac.PLAY;}};Ib.getInstance().on(Ob.EV_AUDIO_RINGER_MODE_CHANGED, this.onAudioMuteChanged);this.onAudioSessionChanged=/*<@73>*/(/*<@2>*/tc, /*<@3>*/uc) =>{if(this.isDestroyed || !this.webview) {return;}Pb.I(Xb, "onAudioSessionChanged,", tc, uc, this.voiceCommend, this.lastState, this.isPageActivated);if(tc) {if(!this.isPageActivated) {return;}if(this.voiceCommend===bc.PAUSE) {this.voiceCommend=bc.NONE;this.pauseVideo();}else if(this.voiceCommend===bc.PLAY) {this.voiceCommend=bc.NONE;this.playVideo();}else if(this.lastState===ac.PLAY) {this.playVideo();}else {this.webview.evaluateJavaScript(`injectJsObject.notifyAudioSession(${JSON.stringify(1)})`);}}else {if(Ib.getInstance().isMediaGainAudioSession()) {this.lastState=ac.PAUSE;}else {this.setLastState();}Pb.I(Xb, "onAudioSessionChanged, lastState:", this.lastState);this.webview.evaluateJavaScript(`injectJsObject.notifyAudioSession(${JSON.stringify(0)})`);this.pauseVideo();}};Ib.getInstance().on(Ob.EV_AUDIO_SESSION_CHANGED, this.onAudioSessionChanged);this.onConnectStateChanged=/*<@74>*/(/*<@1>*/tc) =>{if(this.isDestroyed || !this.webview) {return;}Pb.I(Xb, "onConnectStateChanged, connState:", tc);this.webview.evaluateJavaScript(`injectJsObject.notifyNetworkStatus(${JSON.stringify(tc)})`);};Mb.getInstance().on(Ob.EV_NETWORK_CONNECT_STATE_CHANGED, this.onConnectStateChanged);this.onTrafficChanged=/*<@75>*/(/*<@2>*/tc) =>{if(this.isDestroyed || !this.webview) {return;}Pb.I(Xb, "onTrafficChanged, enable:", tc);if(!tc) {this.pauseVideo();}};Rb.getInstance().on(Ob.EV_TRAFFIC_CHANGED, this.onTrafficChanged);}/*<@45>*/injectJsObject(){let /*<@36>*/tc = this;let /*<@87>*/uc = /*<@87>*/{getVehicleInfo:/*<@76>*/function (){Pb.I(Xb, "getVehicleInfo");let /*<@88>*/vc = /*<@88>*/{product:Kb.getInstance().product,resolution:Kb.getInstance().resolution,orientation:Kb.getInstance().orientation,uuid:Qb.md5(Kb.getInstance().uuid),accountId:Qb.md5(Hb.getInstance().userName)};return JSON.stringify(vc);},getVehicleGearStatus:/*<@77>*/function (){Pb.I(Xb, "getVehicleGearStatus");return Nb.getInstance().getVehicleGearStatus();},getVehicleSpeed:/*<@78>*/function (){Pb.I(Xb, "getVehicleSpeed");return Nb.getInstance().getVehicleSpeed();},getAudioVolumeLevel:/*<@79>*/function (){Pb.I(Xb, "getAudioVolumeLevel");return Ib.getInstance().getAudioVolumeLevel();},setAudioVolumeLevel:/*<@80>*/function (/*<@1>*/vc){Pb.I(Xb, "setAudioVolumeLevel");Ib.getInstance().setAudioVolumeLevel(vc);},getScreenBrightness:/*<@81>*/function (){Pb.I(Xb, "getScreenBrightness");return Jb.getInstance().getScreenBrightness();},setScreenBrightness:/*<@82>*/function (/*<@1>*/vc){Pb.I(Xb, "setScreenBrightness");Jb.getInstance().setScreenBrightness(vc);},getNetworkStatus:/*<@83>*/function (){Pb.I(Xb, "getNetworkStatus");return Mb.getInstance().getNetworkConnState();},getNetworkValid:/*<@84>*/function (){Pb.I(Xb, "getNetworkValid");return Mb.getInstance().isNetworkValid();},requestAudioSession:/*<@85>*/function (){let /*<@1>*/vc = Ib.getInstance().requestAudioSession();Pb.I(Xb, "requestAudioSession, session:", vc);if(vc===1) {tc.playVideo();}return vc===1 ? 1 : 0;},getAmbientLightStatus:/*<@86>*/function (){let /*<@2>*/vc = Nb.getInstance().getAmbientLightStatus();Pb.I(Xb, "getAmbientLightStatus, isEnable:", vc);return vc;}};this.webview.addJavascriptInterface(uc, "injectJsObject", /*<@89>*/["getVehicleInfo","getVehicleGearStatus","getVehicleSpeed","getAudioVolumeLevel","setAudioVolumeLevel","getScreenBrightness","setScreenBrightness","getNetworkStatus","getNetworkValid","requestAudioSession","getAmbientLightStatus","sendOperateEvent","notifyAppEvent","notifyNetworkStatus",]);}/*<@46>*/callJsObject(){let tc = new Ab();tc.onPageFinished=/*<@90>*/(/*<@38>*/uc, /*<@3>*/vc) =>{Pb.D(Xb, "onPageFinished,", vc);this.loadBridge(uc);this.emit(Ob.EV_WEBPAGE_STATUS_CHANGED, true);};tc.onConsoleMessage=/*<@91>*/(/*<@1>*/uc, /*<@3>*/vc, /*<@1>*/wc, /*<@3>*/xc) =>{Pb.D(Xb, "onConsoleMessage,", vc);this.handleJSMessage(vc);};this.webview.client=tc;}/*<@47>*/loadBridge(/*<@38>*/tc){tc.evaluateJavaScript("window.injectJsObject.setMuteStatus = (...args) => {console.log('setMuteStatus:', args)};"+"window.injectJsObject.setScreenMode = (...args) => {console.log('setScreenMode:', args)};"+"window.injectJsObject.sendMessage = (...args) => {console.log('sendMessage:', JSON.stringify(args))};");}/*<@48>*/handleJSMessage(/*<@3>*/tc){if(Qb.isEmpty(tc)) {return;}let /*<@1>*/uc = tc.indexOf("setScreenMode: ");if(uc > -1) {if(tc.substring(uc+"setScreenMode: ".length)==="1") {this.setScreenMode(1);}else {this.setScreenMode(0);}return;}uc=tc.indexOf("setMuteStatus: ");if(uc > -1) {if(tc.substring(uc+"setMuteStatus: ".length)==="1") {this.setMuteStatus(1);}else {this.setMuteStatus(0);}return;}uc=tc.indexOf("sendOperateEvent: ");if(uc > -1) {let /*<@3>*/vc = tc.substring(uc+"sendOperateEvent: ".length);if(vc) {try {let wc = JSON.parse(vc);if(wc) {this.sendOperateEvent(wc);this.videoInfo.title=wc.title;this.videoInfo.src=wc.src;this.videoInfo.operate=wc.operate;if(wc.operate===ac.PLAY) {if(!this.playCondition()) {this.pauseVideo();}}}}catch (wc) {Pb.E(Xb, "sendOperateEvent failed", wc);}}return;}uc=tc.indexOf("sendMessage: ");if(uc > -1) {let /*<@3>*/vc = tc.substring(uc+"sendMessage: ".length);if(vc) {try {let /*<@92>*/wc = JSON.parse(vc);if(wc) {if(wc[0]==="videoSize") {if(wc[1]) {let /*<@1>*/xc = wc[1].width;let /*<@1>*/yc = wc[1].height;this.videoInfo.width=xc>0 ? xc : 0;this.videoInfo.height=yc>0 ? yc : 0;}}}}catch (wc) {Pb.E(Xb, "sendMessage failed", wc);}}return;}}/*<@49>*/setScreenMode(/*<@1>*/tc){Pb.I(Xb, "setScreenMode, value:", tc);if(tc===1) {this.isPlayPage=true;this.removeTimeout(this.delayShowCloseImageTimer);Wb.hideCloseImage();this.webview.evaluateJavaScript(cc);Rb.getInstance().checkTrafficState();this.setCheckFlowTimer();this.addVoiceCommands();}else {this.isPlayPage=false;this.removeTimeout(this.delayShowCloseImageTimer);this.delayShowCloseImageTimer=setTimeout(/*<@95>*/() =>{Wb.showCloseImage();this.removeTimeout(this.delayShowCloseImageTimer);}, Zb);if(this.videoInfo) {this.videoInfo.operate="end";this.sendOperateEvent(this.videoInfo);}this.removeCheckFlowTimer();this.removeVoiceCommands();}}/*<@50>*/setMuteStatus(/*<@1>*/tc){Pb.I(Xb, "setMuteStatus, value:", tc);if(tc===0) {Ib.getInstance().setRingerModeNormal();if(!this.playCondition()) {this.pauseVideo();}}}/*<@51>*/sendOperateEvent(tc){if(!tc) {return;}Sb.getInstance().sendEvent("main_page", "bilibili_play", /*<@96>*/{video_title:tc.title ? tc.title : " ",video_url:tc.src ? tc.src : " ",operation:tc.operate ? tc.operate : " "});}/*<@52>*/playCondition(){if(!this.isPageActivated) {Pb.I(Xb, "playCondition, page is deactivate");return false;}if(!this.isPlayPage) {Pb.I(Xb, "playCondition, it is not play page");return false;}if(!Nb.getInstance().isParkingStatus()) {Pb.I(Xb, "playCondition, it is not parking status");return false;}if(Nb.getInstance().isDrivingWarning()) {Pb.I(Xb, "canPlay, driving warning");this.showWarningPage();return false;}let /*<@2>*/tc = Ib.getInstance().isAppGainAudioSession();Pb.I(Xb, "playCondition, appGainAudioSession:", tc);if(!tc) {let /*<@1>*/uc = Ib.getInstance().requestAudioSession();if(uc!==1) {Pb.I(Xb, "playCondition, request audio session failed,", uc);this.webview.evaluateJavaScript(`injectJsObject.notifyAudioSession(${JSON.stringify(0)})`);return false;}}return true;}/*<@54>*/showWarningPage(){Pb.I(Xb, "videoLimitTag showWarningPage");if(!this.isPlayPage) {return;}if(!this.videoInfo) {return;}Pb.I(Xb, "videoLimitTag showWarningPage1");if(this.videoInfo.operate!==ac.PLAY) {return;}Pb.I(Xb, "videoLimitTag showWarningPage2");if(this.warningPageShowing) {return;}Pb.I(Xb, "videoLimitTag showWarningPage3");this.warningPageShowing=true;if(!this.warningPage) {let uc = new Ub();this.warningPage=uc;this.rootview.addChild(uc);}let /*<@97>*/tc = /*<@97>*/() =>{Pb.D(Xb, `user clicked the button`);let uc = new Eb();uc.uri="page://systemsetting.ivi.com/systemsetting";uc.eventName="general";uc.data=JSON.stringify(/*<@98>*/{locate:"videoStatus"});Db.getInstance().sendLink(uc, /*<@99>*/(/*<@100>*/vc, /*<@100>*/wc) =>{Pb.D(Xb, "page sendlink error:", vc);Pb.D(Xb, "page sendlink result:", wc);});this.hideWarningPage(false);};this.warningPage.show(true, tc, /*<@101>*/() =>{this.hideWarningPage(true);});this.pauseVideo();}/*<@55>*/hideWarningPage(/*<@2>*/tc){if(!this.warningPageShowing) {return;}this.warningPageShowing=false;this.warningPage.hide();this.removeInterval(this.hideWarningTimer);Pb.D(Xb, "hideWarningPage,", tc);if(tc) {this.onBackPressed();}}/*<@56>*/onBackPressed(){if(this.isDestroyed || !this.webview) {return;}if(!this.isPlayPage) {Pb.I(Xb, "goBack, it is not play page");return;}this.setScreenMode(0);this.webview.url=Ob.HOME_URL;}/*<@57>*/removeInterval(tc){if(tc) {clearInterval(tc);tc=null;}}/*<@58>*/playVideo(){if(this.isDestroyed || !this.webview) {return;}if(!this.playCondition()) {return;}Pb.I(Xb, "playVideo");this.webview.evaluateJavaScript(`injectJsObject.notifyAudioSession(${JSON.stringify(1)})`);this.webview.evaluateJavaScript(`injectJsObject.notifyCustom(${JSON.stringify(/*<@102>*/{"type":"play"})})`);}/*<@59>*/pauseVideo(){if(this.isDestroyed || !this.webview) {return;}if(!this.isPlayPage) {Pb.I(Xb, "pauseVideo, it is not play page");return;}Pb.I(Xb, "pauseVideo");this.webview.evaluateJavaScript(`injectJsObject.notifyCustom(${JSON.stringify(/*<@102>*/{"type":"pause"})})`);}/*<@60>*/setLastState(){if(this.videoInfo&&this.videoInfo.operate) {Pb.I(Xb, "setLastState, operate:", this.videoInfo.operate);if(this.videoInfo.operate===ac.PAUSE) {this.lastState=ac.PAUSE;}else {this.lastState=ac.PLAY;}}Pb.I(Xb, "setLastState, lastState:", this.lastState);}/*<@61>*/setCheckFlowTimer(){this.removeCheckFlowTimer();this.checkFlowTimer=setInterval(/*<@103>*/() =>{if(this.isDestroyed || !this.isPageActivated || !this.webview) {return;}Rb.getInstance().checkTrafficState();}, Yb);}/*<@62>*/removeCheckFlowTimer(){if(this.checkFlowTimer) {clearInterval(this.checkFlowTimer);this.checkFlowTimer=null;}}/*<@63>*/notifyAppEvent(/*<@3>*/tc){if(this.isDestroyed || !this.webview) {return;}Pb.I(Xb, "notifyAppEvent, status:", tc);if(tc==="show") {this.webview.focus();}else if(tc==="hide") {this.isPageActivated=false;}else if(tc==="activate") {this.isPageActivated=true;let /*<@2>*/uc = Rb.getInstance().checkTrafficState();if(!uc) {return;}let /*<@1>*/vc = Nb.getInstance().getVehicleGearStatus();Pb.I(Xb, "notifyAppEvent, gearStatus:", vc);this.webview.evaluateJavaScript(`injectJsObject.notifyVehicleGearStatus(${JSON.stringify(vc)})`);}else if(tc==="deactivate") {this.isPageActivated=false;if(Ib.getInstance().isAppGainAudioSession()) {this.setLastState();}}this.webview.evaluateJavaScript(`injectJsObject.notifyAppEvent(${JSON.stringify(tc)})`);}/*<@64>*/addVoiceCommands(){if(this.webview.voiceSelectMode===wb.VoiceSelectMode.Custom) {Pb.I(Xb, "addVoiceCommands, already registered");return;}Pb.I(Xb, "addVoiceCommands");const /*<@89>*/tc = /*<@89>*/["VOICECMD_PLAY_1","VOICECMD_PLAY_2"];const /*<@1>*/uc = tc.length;tc.push("VOICECMD_PAUSE_1", "VOICECMD_PAUSE_2");Qb.registerVoiceCommand(this.webview, tc, yb.Both, /*<@104>*/(/*<@3>*/vc, /*<@1>*/wc) =>{if(this.isDestroyed || !this.isPageActivated) {Pb.W(Xb, "voice command, ignore");return;}Pb.I(Xb, "voice command,", vc, wc);if(wc>=0&&wc<uc) {this.voiceCommend=bc.PLAY;this.playVideo();}else {this.voiceCommend=bc.PAUSE;this.pauseVideo();}}, true);}/*<@65>*/removeVoiceCommands(){if(!this.webview) {Pb.W(Xb, "removeVoiceCommands, views is null");return;}Pb.I(Xb, "removeVoiceCommands");this.webview.removeAllVoiceCommands();Qb.removeVoiceCommand(this.webview);}/*<@66>*/removeTimeout(tc){if(tc) {clearTimeout(tc);tc=null;}}/*<@67>*/destroy(){super.destroy();Pb.I(Xb, "destroy");this.isDestroyed=true;this.removeTimeout(this.delayShowCloseImageTimer);Ib.getInstance().off(Ob.EV_AUDIO_SESSION_CHANGED, this.onAudioSessionChanged);Ib.getInstance().off(Ob.EV_AUDIO_RINGER_MODE_CHANGED, this.onAudioMuteChanged);Ib.releaseInstance();Jb.releaseInstance();Lb.getInstance().off(Ob.EV_HARDKEY_STATE_CHANGED, this.onHardKeyStateChanged);Lb.releaseInstance();Mb.getInstance().off(Ob.EV_NETWORK_CONNECT_STATE_CHANGED, this.onConnectStateChanged);Mb.releaseInstance();Nb.getInstance().off(Ob.EV_GEAR_STATUS_CHANGED, this.onGearStatusChanged);Nb.getInstance().off(Ob.EV_SPEED_CHANGED, this.onSpeedChanged);Nb.releaseInstance();Rb.getInstance().off(Ob.EV_TRAFFIC_CHANGED, this.onTrafficChanged);Hb.releaseInstance();}}module.exports=ContainerBridge;
