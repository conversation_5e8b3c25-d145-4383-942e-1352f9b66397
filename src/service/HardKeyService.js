/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/HardKeyService.js"},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/ui/view/WebView.js","name":""},{"path":"/usr/framework/yunos/core/EventEmitter.js","name":""},{"path":"AudioService.js","name":""},{"path":"../Consts.js","name":""},{"path":"../utils/log.js","name":""},{"aname":"","element":1,"mode":"normal"},{"aname":"","element":0,"mode":"normal"},{"cname":"InputListener","supers":{"Z.InputEventListener":0},"fields":{"handleMessage":0},"methods":[15,16,17]},{"fname":"constructor","ret":14},{"fname":"setHandleMessage","params":{"tc":0}},{"fname":"onKeyEvent","params":{"tc":3,"uc":1}},{"bname":"Object"},{"aname":"","element":18,"mode":"normal"},{"iname":"IInputEntrepotManager","fields":{"createManagerInstance":0,"registerInputListener":0,"unRegisterInputListener":0}},{"cname":"HardKeyService","supers":{"X":8},"fields":{"inputEntrepotManager":20,"inputListener":14,"webview":23,"keydownListener":0,"keyupListener":0},"sfields":{"static instance":21,"const getInstance":24,"const releaseInstance":25},"methods":[22,26,27,28,29,30]},{"fname":"constructor","ret":21,"hobj":1},{"instance_of":7},{"fname":"static getInstance","ret":21},{"fname":"static releaseInstance"},{"fname":"init","params":{"tc":23}},{"fname":"registerKeyEvent","private":1,"params":{"tc":23}},{"fname":"registerHardKeycode","private":1},{"fname":"onHardkeyStateChange","private":1,"params":{"tc":1,"uc":1}},{"fname":"destroy","hobj":1},{"fname":"","params":{"uc":32}},{"oname":"","fields":{"key":-18,"preventDefault":0}},{"fname":"","params":{"uc":32}}],"exports":22}*/"use strict";const /*<@8>*/X = require("/usr/framework/yunos/core/EventEmitter.js");const Y = require("/usr/framework/core/system/inputentrepot/KeyCodes.js");const Z = require("/usr/framework/core/system/inputentrepot/InputEntrepotManager.js");const /*<@9>*/aa = require("/opt/app/bilibili.alios.cn/src/service/AudioService.js");const /*<@10>*/ba = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@11>*/ca = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@3>*/da = "HardKeyService";const /*<@3>*/ea = "MediaNext";const /*<@3>*/fa = "MediaPrevious";const /*<@3>*/ga = "MediaPlayPause";const /*<@12>*/ha = /*<@12>*/[Y.KEYCODE.KEY_MEDIA_NEXT,Y.KEYCODE.KEY_MEDIA_PREVIOUS,Y.KEYCODE.KEY_MEDIA_PLAY_PAUSE];var HardKeyState;(function (HardKeyState){HardKeyState[HardKeyState["KEY_STATE_RELEASED"]=0]="KEY_STATE_RELEASED";HardKeyState[HardKeyState["KEY_STATE_PRESSED"]=1]="KEY_STATE_PRESSED";HardKeyState[HardKeyState["KEY_STATE_REPEATED"]=2]="KEY_STATE_REPEATED";HardKeyState[HardKeyState["KEY_STATE_LONGPRESSED"]=3]="KEY_STATE_LONGPRESSED";HardKeyState[HardKeyState["KEY_STATE_CANCEL"]=4]="KEY_STATE_CANCEL";HardKeyState[HardKeyState["KEY_STATE_LONGPRELEASED"]=5]="KEY_STATE_LONGPRELEASED";})(HardKeyState||(HardKeyState={}));class /*<@15>*/InputListener extends Z.InputEventListener{/*<@16>*/setHandleMessage(tc){this.handleMessage=tc;}/*<@17>*/onKeyEvent(/*<@3>*/tc, /*<@1>*/uc){ca.I(da, "onkeyevent hardkey code:", tc, ", state:", uc);this.handleMessage(tc, uc);}}let /*<@5>*/ia = 0;let /*<@2>*/ja = false;let /*<@5>*/ka = 0;class /*<@22>*/HardKeyService extends X{constructor(){super();}static /*<@24>*/getInstance(){if(!this.instance) {this.instance=new HardKeyService();}return this.instance;}static /*<@25>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@26>*/init(/*<@23>*/tc){if(tc) {this.registerKeyEvent(tc);}else {this.registerHardKeycode();}}/*<@27>*/registerKeyEvent(/*<@23>*/tc){ca.I(da, "registerKeyEvent");tc.on("keydown", this.keydownListener=/*<@31>*/(/*<@32>*/uc) =>{let /*<@2>*/vc = aa.getInstance().isAppGainAudioSession();if(!vc) {return;}ca.I(da, "keydown, keyevent:", uc.key);if(uc.key===ea||uc.key===fa||uc.key===ga) {uc.preventDefault();}});tc.on("keyup", this.keyupListener=/*<@33>*/(/*<@6>*/uc) =>{let /*<@2>*/vc = aa.getInstance().isAppGainAudioSession();if(!vc) {return;}ca.I(da, "keyup, keyevent:", uc.key);if(uc.key===ea) {this.emit(ba.EV_HARDKEY_STATE_CHANGED, ba.HardKeyOperate.NEXT);uc.preventDefault();}else if(uc.key===fa) {this.emit(ba.EV_HARDKEY_STATE_CHANGED, ba.HardKeyOperate.PREV);uc.preventDefault();}else if(uc.key===ga) {this.emit(ba.EV_HARDKEY_STATE_CHANGED, ba.HardKeyOperate.PLAY_PAUSE);uc.preventDefault();}});this.webview=tc;}/*<@28>*/registerHardKeycode(){ca.I(da, "registerHardKeycode");this.inputEntrepotManager=Z.createManagerInstance();this.inputListener=new InputListener();this.inputListener.setHandleMessage(this.onHardkeyStateChange.bind(this));this.inputEntrepotManager.registerInputListener(ha, this.inputListener);}/*<@29>*/onHardkeyStateChange(/*<@1>*/tc, /*<@1>*/uc){ca.I(da, "onHardkeyStateChange code:", tc, ", state:", uc);let /*<@3>*/vc = "";let /*<@5>*/wc = 0;if(uc==1) {ja=false;ia=0;ka=new Date().getTime();}else if(uc==0 && !ja) {switch (tc) {case Y.KEYCODE.KEY_MEDIA_NEXT:vc=ba.HardKeyOperate.NEXT;break;case Y.KEYCODE.KEY_MEDIA_PREVIOUS:vc=ba.HardKeyOperate.PREV;break;case Y.KEYCODE.KEY_MEDIA_PLAY_PAUSE:vc=ba.HardKeyOperate.PLAY_PAUSE;break;default:ca.D(da, "bad key!!");break;}ja=true;}else if(uc==2) {ja=true;let /*<@1>*/xc = new Date().getTime();let /*<@1>*/yc = xc-ka;ca.I(da, "onHardkeyStateChange, code:", tc, ", repeatCount:", ia, ", elapse:", yc);let /*<@5>*/zc = 0;let /*<@5>*/Ac = 0;if(ia==4) {zc=4;}else if(ia>=6&&ia<22) {zc=8;}else if(ia>=22) {zc=32;}if(tc==Y.KEYCODE.KEY_MEDIA_NEXT) {Ac=1*zc;}else if(tc==Y.KEYCODE.KEY_MEDIA_PREVIOUS) {Ac=-1*zc;}else {Ac=0;}if(ia%2==0&&Ac!=0) {ca.I(da, "seek:", Ac);vc="seek";wc=Ac;}ia++;}ca.I(da, "onHardkeyStateChange, methodName:", vc, ", value:", wc);if(vc) {this.emit(ba.EV_HARDKEY_STATE_CHANGED, vc, wc);}}/*<@30>*/destroy(){super.destroy();ca.I(da, "destroy");if(this.inputEntrepotManager&&this.inputListener) {this.inputEntrepotManager.unRegisterInputListener(this.inputListener);this.inputListener=null;this.inputEntrepotManager=null;}if(this.webview) {if(this.keydownListener) {this.webview.off("keydown", this.keydownListener);}if(this.keyupListener) {this.webview.off("keyup", this.keyupListener);}}}}module.exports=HardKeyService;
