/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/DeviceService.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/sysprop/sysprop.js","name":""},{"cname":"DeviceService","sfields":{"static instance":8,"const getInstance":10},"methods":[9,11,12,13,14,15]},{"fname":"constructor","ret":8},{"fname":"static getInstance","ret":8},{"fname":"get vin","ret":3},{"fname":"get uuid","ret":3},{"fname":"get product","ret":3},{"fname":"get resolution","ret":3},{"fname":"get orientation","ret":3}],"exports":9}*/"use strict";const /*<@7>*/V = require("/usr/framework/sysprop/sysprop.js");class /*<@9>*/DeviceService{static /*<@10>*/getInstance(){if(!this.instance) {this.instance=new DeviceService();}return this.instance;}get /*<@11>*/vin(){return V.get("persist.sys.vin", "");}get /*<@12>*/uuid(){return V.get("ro.yunos.clouduuid", "");}get /*<@13>*/product(){return V.get("ro.yunos.product", "");}get /*<@14>*/resolution(){return V.get("sys.yunos.resolution.output0", "");}get /*<@15>*/orientation(){return V.get("sys.yunos.orientation", "");}}module.exports=DeviceService;
