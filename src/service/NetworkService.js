/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/NetworkService.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/core/EventEmitter.js","name":""},{"path":"/usr/framework/yunos/net/linkclient/LinkClient.js","name":""},{"path":"/usr/framework/yunos/net/linkclient/NetStatus.js","name":""},{"path":"/usr/framework/sysprop/sysprop.js","name":""},{"path":"../Consts.js","name":""},{"path":"../utils/log.js","name":""},{"cname":"NetworkService","supers":{"la":7},"fields":{"trafficCtrlEnabled":2,"onTrafficCtrlChanged":0,"onNetworkStatusChanged":0},"sfields":{"static instance":13,"const getInstance":15,"const releaseInstance":16},"methods":[14,17,18,19,20,21,22]},{"fname":"constructor","ret":13},{"fname":"static getInstance","ret":13},{"fname":"static releaseInstance"},{"fname":"init"},{"fname":"getNetworkConnState","ret":1},{"fname":"isNetworkValid","ret":2},{"fname":"isNetworkWiFi","ret":2},{"fname":"isTrafficCtrlEnabled","ret":2},{"fname":"destroy","hobj":1},{"bname":"Object"},{"fname":"","params":{"tc":25}},{"instance_of":9},{"fname":"","params":{"tc":2}}],"exports":14}*/"use strict";const /*<@7>*/la = require("/usr/framework/yunos/core/EventEmitter.js");const /*<@8>*/ma = require("/usr/framework/yunos/net/linkclient/LinkClient.js");const /*<@10>*/oa = require("/usr/framework/sysprop/sysprop.js");const /*<@11>*/pa = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@12>*/qa = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@3>*/ra = "NetworkService";class /*<@14>*/NetworkService extends la{static /*<@15>*/getInstance(){if(!this.instance) {this.instance=new NetworkService();}return this.instance;}static /*<@16>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@17>*/init(){this.onNetworkStatusChanged=/*<@24>*/(/*<@25>*/tc) =>{if(!tc) {qa.E(ra, "onNetworkStatusChanged failed, ns invalid, ns:", tc);return;}let uc = ma.getInstance().getActiveNetworkStatus();let /*<@1>*/vc = uc ? uc.connState : tc.connState;qa.D(ra, "onNetworkStatusChanged, connState:", vc);this.emit(pa.EV_NETWORK_CONNECT_STATE_CHANGED, vc);};ma.getInstance().addListener(this.onNetworkStatusChanged);this.trafficCtrlEnabled=ma.getInstance().getTrafficCtrlState();qa.D(ra, "trafficCtrlState", this.trafficCtrlEnabled);this.onTrafficCtrlChanged=/*<@26>*/(/*<@2>*/tc) =>{qa.D(ra, "onTrafficCtrlChanged, enable", tc);this.trafficCtrlEnabled=tc;};ma.getInstance().on("trafficctrlchanged", this.onTrafficCtrlChanged);}/*<@18>*/getNetworkConnState(){let tc = ma.getInstance().getActiveNetworkStatus();if(tc) {return tc.connState;}return ma.State.DISCONNECTED;}/*<@19>*/isNetworkValid(){return oa.get("network.status", "-1")!=="1";}/*<@20>*/isNetworkWiFi(){let tc = ma.getInstance().getActiveNetworkStatus();if(tc) {qa.I(ra, "ns.connState:", tc.connState, ", ns.netType:", tc.netType);if(tc.connState===ma.State.CONNECTED&&tc.netType===ma.NetworkType.TYPE_WIFI) {return true;}}return false;}/*<@21>*/isTrafficCtrlEnabled(){return this.trafficCtrlEnabled;}/*<@22>*/destroy(){super.destroy();qa.I(ra, "destroy");if(this.onTrafficCtrlChanged) {ma.getInstance().off("trafficctrlchanged", this.onTrafficCtrlChanged);}if(this.onNetworkStatusChanged) {ma.getInstance().removeListener(this.onNetworkStatusChanged);}}}module.exports=NetworkService;
